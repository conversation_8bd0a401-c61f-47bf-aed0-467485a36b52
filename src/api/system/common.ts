import { http } from "@/utils/http";

/**
 * 更新用户分页大小存储
 * @param data
 */
export const headerPageUpdate = (data?: object) => {
  return http.request<any>("post", "/api/header-page/update", undefined, {
    data: data
  });
};

/**
 * 获取用户分页大小存储
 */
export const getHeaderPage = sysMenu => {
  return http.request<any>("get", `/api/header-page/${sysMenu}`);
};

/**
 * 获取用户筛选方案
 */
export const querySchemeList = data => {
  return http.request<any>("get", `/api/query-scheme/list`, { params: data });
};

/**
 * 添加用户筛选方案
 */
export const querySchemeAdd = data => {
  return http.request<any>("post", `/api/query-scheme/add`, undefined, {
    data
  });
};
