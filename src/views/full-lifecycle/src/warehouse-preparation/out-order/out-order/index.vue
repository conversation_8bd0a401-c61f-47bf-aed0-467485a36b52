<template>
  <div>
    <ReTable
      ref="tableRef"
      :mappingStatus="salesOrderStateMap"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      showTotal
      :getTotal="getTotal"
      :headerCellKeys="[]"
      :tableRowWarnStatus="tableRowWarnStatus"
      :status="[]"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import { useRoute, useRouter } from "vue-router";
import { ref, h, reactive, watch, onMounted } from "vue";
import { fullLifecycleOutOrderDetailSpecialId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";

import {
  getOutOrderAPI,
  getOutOrderFieldAPI,
  getOutOrderAPITotalAPI,
  exportOutOrderAPI,
  getOutOrderFieldByTableIdAPI
} from "@/api/full-lifecycle/warehouse-preparation/out-order";
// 求和hook

/** 表格状态数据 */
import { salesOrderStateMap } from "@/views/full-lifecycle/utils";

// 导入获取billNo相关hook
import { useFetchTableData, useParentColumnTag } from "@/utils/hooks";
import { useNodeStore } from "@/store/modules/fullLifecycle";

const { setParentColumnTag } = useParentColumnTag();
const router = useRouter();
const nodeStore = useNodeStore();
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
// 定义表格配置

// 搜素内容
const queryData = ref({
  page: { current: 1, size: 20, default: 20 },
  query: [],
  order: [
    {
      name: "purOrderNo",
      sort: "asc"
    },
    {
      name: "purOrderEntrySeq",
      sort: "asc"
    }
  ]
});

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  fullLifecycleOutOrderDetailSpecialId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息
getSortedColumnHeaders(
  getOutOrderFieldAPI,
  columns,
  getOutOrderFieldByTableIdAPI,
  fullLifecycleOutOrderDetailSpecialId
);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(getOutOrderAPI, {
  states: salesOrderStateMap,
  addPercentSigns: [
    "inboundCompletionRate",
    "inspectionCompletionRate",
    "deliveryCompletionRate",
    "inventoryCompletionRate"
  ]
});

const tableRowWarnStatus = reactive([
  {
    columnKey: "inboundCompletionRate",
    operator: "<",
    threshold: 100,
    className: "row-pending",
    sift: "%",
    relation: "and",
    children: [
      {
        columnKey: "closeStatusCaption",
        operator: "==",
        threshold: "未关闭",
        className: "row-pending",
        sift: "and"
      }
    ]
  },
  {
    columnKey: "colorStatu",
    operator: "==",
    threshold: "1",
    className: "row-processing"
  }
]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportOutOrderAPI, {
        queryId: nodeStore.queryId
      });
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 需要求和字段数组
const sumList = ref([""]);

/**
 * @function 获取求和函数
 * @param {Array} sumList 需要求和字段数组
 * @param {Function} getTotalAPI 获取求和的API
 */
const getTotal = getTotalHook(sumList, getOutOrderAPITotalAPI, {
  queryId: nodeStore.queryId
});

// 某行被点击
const handleRowClick = (row, column, cell) => {
  const columnList = ["billNo"];
  setParentColumnTag(
    row,
    columns.value,
    columnList,
    "outOrderDetailColumnFields"
  );
  nodeStore.setSonOutOrderDetailBillNo(row.billNo);
  router.push({
    path: "/full-lifecycle/out-order-detail"
  });
  console.log(row, column, cell, "某行被点击");
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
};
</script>
