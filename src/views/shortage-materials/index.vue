<template>
  <div>
    <div class="flex justify-end mt-8">
      <el-button
        type="primary"
        size="small"
        @click="materialsDialogVisible = true"
      >
        物料编码筛选
      </el-button>
      <el-button
        type="primary"
        size="small"
        @click="salesOrderDialogVisible = true"
      >
        销售订单筛选
      </el-button>
    </div>
    <vxe-table
      :key="tableKey"
      class="mt-1"
      :scroll-x="{
        enabled: true,
        gt: 0,
        mode: 'wheel',
        scrollToLeftOnChange: false
      }"
      :scroll-y="{ enabled: true, gt: 0, mode: 'wheel' }"
      show-overflow
      border
      :round="true"
      :height="windowHeight"
      :data="filteredTableData"
      :resizable-config="{ minWidth: 40 }"
      :row-config="{ isCurrent: true, isHover: true }"
      :loading="isRefreshing"
      @resizable-change="handleResizableChange"
      @cell-dblclick="handleCellClick"
    >
      <vxe-column
        fixed="left"
        type="checkbox"
        :width="getColumnWidth('left', 50)"
      />
      <vxe-column
        fixed="left"
        type="seq"
        :width="getColumnWidth('seq', 60)"
        resizable
      />
      <vxe-column
        fixed="left"
        field="materialId"
        title="物料编码"
        :width="getColumnWidth('materialId', 100)"
        resizable
        sortable
      >
        <template #header>
          <div class="column-header">
            <div>物料编码</div>
            <vxe-input
              v-model="filters.materialId"
              style="align-items: center; height: 26px"
              placeholder="搜索"
              clearable
              @input="handleFilterChange"
            />
          </div>
        </template>
      </vxe-column>
      <vxe-column
        fixed="left"
        field="materialName"
        title="物料名称"
        :width="getColumnWidth('materialName', 160)"
        resizable
        sortable
      >
        <template #header>
          <div class="column-header">
            <div>物料名称</div>
            <vxe-input
              v-model="filters.materialName"
              style="align-items: center; height: 26px"
              placeholder="搜索"
              clearable
              @input="handleFilterChange"
            />
          </div>
        </template>
      </vxe-column>
      <vxe-column
        fixed="left"
        field="allNotPickedQty"
        title="总未领"
        :width="getColumnWidth('allNotPickedQty', 80)"
        resizable
        sortable
      >
        <template #header>
          <div class="column-header">
            <div>总未领</div>
            <vxe-input
              v-model="filters.allNotPickedQty"
              style="align-items: center; height: 26px"
              placeholder="搜索"
              clearable
              @input="handleFilterChange"
            />
          </div>
        </template>
      </vxe-column>
      <vxe-column
        fixed="left"
        field="allMaterialShortageSituation"
        title="总欠料"
        :width="getColumnWidth('allMaterialShortageSituation', 80)"
        resizable
        sortable
      >
        <template #header>
          <div class="column-header">
            <div>总欠料</div>
            <vxe-input
              v-model="filters.allMaterialShortageSituation"
              style="align-items: center; height: 26px"
              placeholder="搜索"
              clearable
              @input="handleFilterChange"
            />
          </div>
        </template>
      </vxe-column>
      <vxe-column
        fixed="left"
        field="prodNotReceived"
        title="生产未领"
        :width="getColumnWidth('prodNotReceived', 80)"
        resizable
        sortable
      >
        <template #header>
          <div class="column-header">
            <div>生产未领</div>
            <vxe-input
              v-model="filters.prodNotReceived"
              style="align-items: center; height: 26px"
              placeholder="搜索"
              clearable
              @input="handleFilterChange"
            />
          </div>
        </template>
      </vxe-column>
      <vxe-column
        fixed="left"
        field="subNotReceived"
        title="委外未领"
        :width="getColumnWidth('subNotReceived', 80)"
        resizable
        sortable
      >
        <template #header>
          <div class="column-header">
            <div>委外未领</div>
            <vxe-input
              v-model="filters.subNotReceived"
              style="align-items: center; height: 26px"
              placeholder="搜索"
              clearable
              @input="handleFilterChange"
            />
          </div>
        </template>
      </vxe-column>
      <vxe-column
        fixed="left"
        field="justInventory"
        title="库存"
        :width="getColumnWidth('justInventory', 80)"
        resizable
        sortable
      >
        <template #header>
          <div class="column-header">
            <div>即时库存</div>
            <vxe-input
              v-model="filters.justInventory"
              style="align-items: center; height: 26px"
              placeholder="搜索"
              clearable
              @input="handleFilterChange"
            />
          </div>
        </template>
      </vxe-column>
      <vxe-column
        fixed="left"
        field="prodMaking"
        title="生产在制"
        :width="getColumnWidth('prodMaking', 80)"
        resizable
        sortable
      >
        <template #header>
          <div class="column-header">
            <div>生产在制</div>
            <vxe-input
              v-model="filters.prodMaking"
              style="align-items: center; height: 26px"
              placeholder="搜索"
              clearable
              @input="handleFilterChange"
            />
          </div>
        </template>
      </vxe-column>
      <vxe-column
        fixed="left"
        field="purTransit"
        title="采购在途"
        :width="getColumnWidth('purTransit', 80)"
        resizable
        sortable
      >
        <template #header>
          <div class="column-header">
            <div>采购在途</div>
            <vxe-input
              v-model="filters.purTransit"
              style="align-items: center; height: 26px"
              placeholder="搜索"
              clearable
              @input="handleFilterChange"
            />
          </div>
        </template>
      </vxe-column>

      <vxe-colgroup
        v-for="(item, index) in filtersSalesOrder || []"
        :key="item.billNo"
      >
        <template #header>
          <span class="flex justify-between w-full">
            <span>订单交期</span>
            <span>{{ item.deliveryDate || "-" }}</span>
          </span>
        </template>
        <vxe-colgroup :title="item.billNo + '/' + item.sytText1">
          <vxe-column
            v-if="item.status != 2"
            :field="`materialShortageVO[${index}].prodNeedQty`"
            title="生产需求"
            :width="
              getColumnWidth(`materialShortageVO[${index}].prodNeedQty`, 80)
            "
            resizable
            sortable
          >
            <template #header>
              <div class="column-header">
                <div>生产需求</div>
                <vxe-input
                  v-model="filters[`materialShortageVO[${index}].prodNeedQty`]"
                  style="align-items: center; height: 26px"
                  placeholder="搜索"
                  clearable
                  @input="handleFilterChange"
                />
              </div>
            </template>
          </vxe-column>
          <vxe-column
            v-if="item.status != 2"
            :field="`materialShortageVO[${index}].prodNotNeck`"
            title="生产未领"
            :width="
              getColumnWidth(`materialShortageVO[${index}].prodNotNeck`, 80)
            "
            resizable
            sortable
          >
            <template #header>
              <div class="column-header">
                <div>生产未领</div>
                <vxe-input
                  v-model="filters[`materialShortageVO[${index}].prodNotNeck`]"
                  style="align-items: center; height: 26px"
                  placeholder="搜索"
                  clearable
                  @input="handleFilterChange"
                />
              </div>
            </template>
          </vxe-column>
          <vxe-column
            v-if="item.status != 1"
            :field="`materialShortageVO[${index}].subNeedQty`"
            title="委外需求"
            :width="
              getColumnWidth(`materialShortageVO[${index}].subNeedQty`, 80)
            "
            resizable
            sortable
          >
            <template #header>
              <div class="column-header">
                <div>委外需求</div>
                <vxe-input
                  v-model="filters[`materialShortageVO[${index}].subNeedQty`]"
                  style="align-items: center; height: 26px"
                  placeholder="搜索"
                  clearable
                  @input="handleFilterChange"
                />
              </div>
            </template>
          </vxe-column>
          <vxe-column
            v-if="item.status != 1"
            :field="`materialShortageVO[${index}].subNotNeck`"
            title="委外未领"
            :width="
              getColumnWidth(`materialShortageVO[${index}].subNotNeck`, 80)
            "
            resizable
            sortable
          >
            <template #header>
              <div class="column-header">
                <div>委外未领</div>
                <vxe-input
                  v-model="filters[`materialShortageVO[${index}].subNotNeck`]"
                  style="align-items: center; height: 26px"
                  placeholder="搜索"
                  clearable
                  @input="handleFilterChange"
                />
              </div>
            </template>
          </vxe-column>
          <vxe-column
            :field="`materialShortageVO[${index}].orderMaterialShortageSituationQty`"
            title="欠料"
            :width="
              getColumnWidth(
                `materialShortageVO[${index}].orderMaterialShortageSituationQty`,
                80
              )
            "
            resizable
            sortable
          >
            <template #header>
              <div class="column-header">
                <div>
                  <el-select
                    v-model="item.status"
                    placeholder="Select"
                    size="small"
                    style="width: 100px"
                    @change="handleMaterialsTypeChange(item)"
                  >
                    <el-option
                      v-for="item in materialsTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
                <vxe-input
                  v-model="
                    filters[
                      `materialShortageVO[${index}].orderMaterialShortageSituationQty`
                    ]
                  "
                  style="align-items: center; height: 26px"
                  placeholder="搜索"
                  clearable
                  @input="handleFilterChange"
                />
              </div>
            </template>
          </vxe-column>
          <vxe-column
            :field="`materialShortageVO[${index}].backTime`"
            title="欠料回料时间"
            :width="
              getColumnWidth(`materialShortageVO[${index}].backTime`, 120)
            "
            resizable
            sortable
          >
            <template #header>
              <div class="column-header">
                <div>欠料回料时间</div>
                <vxe-date-picker
                  v-model="filters[`materialShortageVO[${index}].backTime`]"
                  type="date"
                  placeholder="选择日期时间"
                  clearable
                  style="align-items: center; height: 26px"
                  @change="handleFilterChange"
                />
              </div>
            </template>
            <template #default="{ row }">
              <el-date-picker
                v-if="!row.materialShortageVO[index].backTime"
                v-model="row.materialShortageVO[index].backTimeInputValue"
                type="datetime"
                placeholder="选择日期时间"
                size="small"
                clearable
                style="align-items: center; width: 100%; height: 26px"
                @change="
                  handleSaveOrderConfiguration(row.materialShortageVO[index], 1)
                "
              />
              <span v-else>{{
                dayjs(row.materialShortageVO[index].backTime).format(
                  "YYYY-MM-DD HH:mm:ss"
                )
              }}</span>
            </template>
          </vxe-column>
          <vxe-column
            :field="`materialShortageVO[${index}].remarks`"
            title="备注"
            :width="getColumnWidth('remarks', 160)"
            resizable
            sortable
          >
            <template #header>
              <div class="column-header">
                <div>备注</div>
                <vxe-input
                  v-model="filters[`materialShortageVO[${index}].remarks`]"
                  style="align-items: center; height: 26px"
                  placeholder="搜索"
                  clearable
                  @input="handleFilterChange"
                />
              </div>
            </template>
            <template #default="{ row }">
              <el-input
                v-if="!row.materialShortageVO[index].remarks"
                v-model="row.materialShortageVO[index].remarksInputValue"
                :autosize="{ minRows: 2, maxRows: 4 }"
                placeholder="请输入"
                @keydown.enter="
                  handleSaveOrderConfiguration(row.materialShortageVO[index], 2)
                "
              >
                <template #append>
                  <el-button
                    style="flex-shrink: 0"
                    type="primary"
                    size="small"
                    @click="
                      handleSaveOrderConfiguration(
                        row.materialShortageVO[index],
                        2
                      )
                    "
                  >
                    保存
                  </el-button>
                </template>
              </el-input>
              <span v-else>{{ row.materialShortageVO[index].remarks }}</span>
            </template>
          </vxe-column>
          <!--          <vxe-column field="leftovers" title="剩余料" width="80" />-->
        </vxe-colgroup>
      </vxe-colgroup>
    </vxe-table>

    <!--  物料编码穿梭框  -->
    <el-dialog
      v-model="materialsDialogVisible"
      draggable
      title="物料编码筛选"
      width="680px"
    >
      <el-transfer
        v-model="materialsDialogVisibleData"
        filterable
        :data="materialsDialogData"
        :titles="['隐藏物料', '显示物料']"
        :button-texts="['隐藏', '显示']"
      />
      <template #footer>
        <div class="flex justify-end gap-3">
          <el-button @click="materialsDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleConfirmMaterialsTransferChange"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!--  销售订单配置穿梭框  -->
    <el-dialog
      v-model="salesOrderDialogVisible"
      draggable
      title="销售订单配置筛选"
      width="680px"
    >
      <el-transfer
        v-model="salesOrderDialogVisibleData"
        filterable
        :data="salesOrderDialogData"
        :titles="['隐藏订单', '显示订单']"
        :button-texts="['隐藏', '显示']"
      />
      <template #footer>
        <div class="flex justify-end gap-3">
          <el-button @click="salesOrderDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleConfirmSalesOrderTransferChange"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="tsx" setup>
import { ref, computed, reactive, onMounted, nextTick } from "vue";
import {
  getMaterialScreeningAPI,
  getMatterMaterialIdAPI,
  getMatterSelectALLAPI,
  getMatterSelectAPI,
  getMatterSelectSaleOrderAPI,
  getOrderScreeningAPI,
  updateMaterialScreeningAPI,
  updateOrderScreeningAPI,
  updateOverageScreeningAPI,
  updOrAddOrderConfigurationAPI
} from "@/api/shortage-materials";
import { debounce } from "lodash-es";
import { cloneDeep } from "@pureadmin/utils";
import router from "@/router";
import { ElInput } from "element-plus";
import dayjs from "dayjs";

const tableData = ref([]);
const filteredTableData = ref([]);
const materialsDialogVisible = ref(false);
const materialsDialogVisibleData = ref([]);
const materialsDialogData = ref([]);
const salesOrderDialogVisible = ref(false);
const salesOrderDialogVisibleData = ref([]);
const salesOrderDialogData = ref([]);
const filtersSalesOrder = ref({});
const isRefreshing = ref(false);
const tableKey = ref(0); // 用于强制重新渲染表格
const materialsTypeOptions = ref([
  {
    label: "全部欠料",
    value: 0
  },
  {
    label: "生产欠料",
    value: 1
  },
  {
    label: "委外欠料",
    value: 2
  }
]);

const handleMaterialsTypeChange = async item => {
  console.log(
    "%cconsole.log[index.vue-564-val, item]: ",
    "color: #ffffff; font-style: italic; background-color: #a76283;padding: 2px",
    "\n",
    item,
    "\n\n ==============console.log-end=============="
  );
  const data = {
    saleOrder: item.billNo,
    status: item.status
  };

  try {
    const res = await updateOverageScreeningAPI(data);
    if (res.code === 200) {
      // 使用当前的筛选条件刷新数据
      await requestMatterSelectData(
        materialsDialogVisibleData.value,
        salesOrderDialogVisibleData.value
      );
    }
  } catch (error) {
    console.error("更新物料类型筛选失败:", error);
  }
};

// 请求物料欠料一览数据
const requestMatterSelectData = async (materialIdList = [], orderList = []) => {
  try {
    isRefreshing.value = true;
    console.log("开始刷新物料欠料数据...", { materialIdList, orderList });

    const params = {
      materialIdList,
      orderList
    };
    const res = await getMatterSelectALLAPI(params);

    if (res.data) {
      // 清空旧数据，确保重新渲染
      tableData.value = [];
      filteredTableData.value = [];

      // 使用 nextTick 确保 DOM 更新后再设置新数据
      await nextTick();

      // 设置新数据
      tableData.value = res.data;
      filteredTableData.value = res.data;

      console.log("物料欠料数据更新完成，记录数:", res.data.length);

      // 重新组装订单数据
      await requestMatterSelectSaleOrder();
    }
  } catch (error) {
    console.error("获取物料欠料数据失败:", error);
  } finally {
    isRefreshing.value = false;
  }
};
requestMatterSelectData();

// 请求所有销售订单列表
const allOrder = ref([]);
const requestMatterSelectSaleOrder = async () => {
  try {
    console.log("开始重新组装订单数据...");
    const res = await getMatterSelectSaleOrderAPI();

    if (res.data) {
      allOrder.value = res.data.records;
      await requestOrderScreeningAPIData();

      // 拼接表格数据和订单数据
      tableData.value.forEach(tableItem => {
        const reMaterialShortageVO = allOrder.value.map(order => {
          return {
            saleOrderNo: order.billNo,
            materialId: tableItem.materialId,
            prodNeedQty: 0,
            prodNotNeck: 0,
            subNeedQty: 0,
            subNotNeck: 0,
            orderMaterialShortageSituationQty: 0,
            backTime: null,
            remarks: "",
            sytText1: order.sytText1,
            deliveryDate: order.deliveryDate,
            deliveryRemainingDay: order.deliveryRemainingDay
          };
        });

        // 如果存在原有的订单数据，则合并
        if (
          tableItem.materialShortageVO &&
          tableItem.materialShortageVO.length > 0
        ) {
          const deepClone = cloneDeep(tableItem.materialShortageVO);
          deepClone.forEach(item => {
            reMaterialShortageVO.forEach(orderItem => {
              if (item.saleOrderNo === orderItem.saleOrderNo) {
                orderItem.prodNeedQty = item.prodNeedQty;
                orderItem.prodNotNeck = item.prodNotNeck;
                orderItem.subNeedQty = item.subNeedQty;
                orderItem.subNotNeck = item.subNotNeck;
                orderItem.orderMaterialShortageSituationQty =
                  item.orderMaterialShortageSituationQty;
                orderItem.backTime = item.backTime;
                orderItem.remarks = item.remarks;
              }
            });
          });
        }

        tableItem.materialShortageVO = reMaterialShortageVO;
      });

      // 强制更新过滤后的数据
      filteredTableData.value = [...tableData.value];

      // 强制重新渲染表格
      tableKey.value += 1;

      console.log("订单数据重新组装完成");
    }
  } catch (error) {
    console.error("重新组装订单数据失败:", error);
  }
};

// 请求所有物料列表
const allMaterial = ref([]);
const requestMatterSelectMaterial = async () => {
  const res = await getMatterMaterialIdAPI();
  if (res.data) {
    allMaterial.value = res.data.records;
    await requestMatterSelectSaleOrderTransferData();
  }
};
requestMatterSelectMaterial();

// 列宽存储
const COLUMN_WIDTHS_STORAGE_KEY = "shortage-materials-column-widths";
const columnWidths = ref({});

// 获取列宽，优先使用保存的宽度，其次使用默认宽度
const getColumnWidth = (field, defaultWidth) => {
  return columnWidths.value[field] || defaultWidth;
};

// 从本地存储加载列宽设置
onMounted(() => {
  try {
    const savedWidths = localStorage.getItem(COLUMN_WIDTHS_STORAGE_KEY);
    if (savedWidths) {
      columnWidths.value = JSON.parse(savedWidths);
    }
  } catch (error) {
    console.error("加载列宽设置失败:", error);
  }
});

// 处理列宽变化
const handleResizableChange = ({ column, resizeWidth }) => {
  if (column.field) {
    // 使用 resizeWidth 参数，这是调整后的新宽度
    columnWidths.value[column.field] = resizeWidth;
    try {
      localStorage.setItem(
        COLUMN_WIDTHS_STORAGE_KEY,
        JSON.stringify(columnWidths.value)
      );
    } catch (error) {
      console.error("保存列宽设置失败:", error);
    }
  }
};

// 计算窗口高度
const windowHeight = computed(() => {
  return (
    (window.innerHeight ||
      document.documentElement.clientHeight ||
      document.body.clientHeight) -
    168 +
    "px"
  );
});

// 处理过滤条件变化
const filters = reactive({}); // 定义过滤条件对象
const handleFilterChange = debounce(() => {
  // 先恢复原始数据
  let filtered = [...tableData.value];

  // 遍历所有过滤条件
  for (const [key, value] of Object.entries(filters)) {
    // 如果过滤值为空，则跳过该条件
    if (!value || value === "") continue;

    // 处理嵌套字段，如 materialShortageVO[0].prodNeedQty
    if (key.includes("materialShortageVO")) {
      const matches = key.match(/materialShortageVO\[(\d+)\]\.(\w+)/);
      if (matches && matches.length === 3) {
        const index = parseInt(matches[1]);
        const field = matches[2];

        filtered = filtered.filter(item => {
          if (
            !item.materialShortageVO ||
            !item.materialShortageVO[index] ||
            item.materialShortageVO[index][field] === undefined
          ) {
            return false;
          }

          const itemValue = String(
            item.materialShortageVO[index][field]
          ).toLowerCase();
          return itemValue.includes(String(value).toLowerCase());
        });
      }
    } else {
      // 处理普通字段
      filtered = filtered.filter(item => {
        if (item[key] === undefined) return false;
        const itemValue = String(item[key]).toLowerCase();
        return itemValue.includes(String(value).toLowerCase());
      });
    }
  }

  // 更新过滤后的数据
  filteredTableData.value = filtered;
}, 300);

// 物料编码穿梭框
const requestMatterSelectSaleOrderTransferData = async () => {
  materialsDialogData.value = allMaterial.value.map(item => {
    return {
      key: item.materialId,
      label: item.materialId
    };
  });
  const res = await getMaterialScreeningAPI();
  if (res.data) {
    materialsDialogVisibleData.value = JSON.parse(res.data.materialIdShow);
  } else {
    const allMaterialClone = cloneDeep(allMaterial.value);
    materialsDialogVisibleData.value = allMaterialClone.map(item => {
      return item.materialId;
    });
  }
};

const requestUpdateMaterialScreeningAPI = async () => {
  const params = {
    materialIdShow: JSON.stringify(materialsDialogVisibleData.value),
    materialIdHide: JSON.stringify(
      materialsDialogData.value
        .map(item => item.key)
        .filter(item => !materialsDialogVisibleData.value.includes(item))
    )
  };
  await updateMaterialScreeningAPI(params);
};

// 确认物料筛选结果
const handleConfirmMaterialsTransferChange = async () => {
  materialsDialogVisible.value = false;
  await requestUpdateMaterialScreeningAPI();
  // requestMatterSelectData 内部已经包含了订单数据重组
  await requestMatterSelectData(
    materialsDialogVisibleData.value,
    salesOrderDialogVisibleData.value
  );
};

// 销售订单配置穿梭框
const requestOrderScreeningAPIData = async () => {
  salesOrderDialogData.value = allOrder.value.map(item => {
    return {
      key: item.billNo,
      label: item.billNo
    };
  });
  const res = await getOrderScreeningAPI();
  if (res.data) {
    salesOrderDialogVisibleData.value = JSON.parse(res.data.saleOrderShow);
    filtersSalesOrder.value = allOrder.value.filter(item => {
      return salesOrderDialogVisibleData.value.includes(item.billNo);
    });
  } else {
    const allOrderClone = cloneDeep(allOrder.value);
    salesOrderDialogVisibleData.value = allOrderClone.map((item, index) => {
      if (index < 3) {
        return item.billNo;
      }
    });
    filtersSalesOrder.value = allOrderClone.slice(0, 3);
  }
};

const requestUpdateOrderScreeningAPI = async () => {
  const params = {
    saleOrderShow: JSON.stringify(salesOrderDialogVisibleData.value),
    saleOrderHide: JSON.stringify(
      salesOrderDialogData.value
        .map(item => item.key)
        .filter(item => !salesOrderDialogVisibleData.value.includes(item))
    )
  };
  await updateOrderScreeningAPI(params);
};

// 确认销售订单筛选结果
const handleConfirmSalesOrderTransferChange = async () => {
  salesOrderDialogVisible.value = false;
  await requestUpdateOrderScreeningAPI();
  // requestMatterSelectData 内部已经包含了订单数据重组和筛选
  await requestMatterSelectData(
    materialsDialogVisibleData.value,
    salesOrderDialogVisibleData.value
  );
};

// 处理单元格点击
const handleCellClick = (row, column, cell) => {
  // 采购在途
  if (row.column.field === "purTransit") {
    // 跳转明细页携带materiaId
    const materialId = row.row.materialId;
    router.push({
      path: "/shortage-materials/detail",
      query: {
        materialId: materialId
      }
    });
  }
};

// 处理 欠料时间和备注 维护
const handleSaveOrderConfiguration = async (data, type) => {
  console.log("保存订单配置:", data, type);
  let params = {
    saleOrder: data.saleOrderNo,
    materialId: data.materialId,
    backTime: "",
    remarks: ""
  };
  if (type === 1) {
    // 欠料时间
    params.backTime = data.backTimeInputValue;
  } else if (type === 2) {
    // 备注
    params.remarks = data.remarksInputValue;
  }

  try {
    const res = await updOrAddOrderConfigurationAPI(params);
    if (res.code === 200) {
      console.log("保存成功，开始刷新表格数据...");
      // 刷新表格数据并重新渲染
      // requestMatterSelectData 内部已经包含了加载状态管理
      await requestMatterSelectData(
        materialsDialogVisibleData.value,
        salesOrderDialogVisibleData.value
      );
      console.log("表格数据刷新完成");
    }
  } catch (error) {
    console.error("保存配置失败:", error);
  }
};
</script>

<style lang="scss" scoped>
:deep(.vxe-table--header) {
  background-color: #f8f8f9;
}

/* 列头部搜索框样式 */
.column-header {
  display: flex;
  flex-direction: column;
  align-items: center;

  /* 确保列标题文本居中 */
  div {
    overflow: hidden;
    font-weight: bold;
    text-align: center;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* 调整输入框样式 */
  :deep(.vxe-input) {
    width: 100%;
    min-width: 40px;
  }

  :deep(.vxe-cell) {
    max-height: none !important;
  }
}

:deep(.vxe-header--row:nth-child(3)) {
  height: 80px;
}

/* 增加列头部高度，以容纳搜索框 */
:deep(.vxe-header--column) {
  min-height: 90px;
  padding: 0 !important;
}

/* 确保表格头部有足够的高度 */
:deep(.vxe-table--header-wrapper) {
  min-height: 90px;
}
</style>
