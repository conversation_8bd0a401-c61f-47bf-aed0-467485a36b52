<template>
  <div class="filter-component">
    <div>
      <div class="flex items-center mb-1">
        <div class="text-sm">我的方案</div>
        <div>
          <el-tag :checked="checked">默认方案</el-tag>
        </div>
      </div>
      <div class="flex items-center">
        <div class="text-sm">快捷过滤</div>
        <filterInput
          ref="filterInput1"
          :columns="columns"
          :index="0"
          :enterFilter="applyFilter"
          :mappingStatus="mappingStatus"
        />
        <filterInput
          ref="filterInput2"
          :columns="columns"
          :index="1"
          :enterFilter="applyFilter"
          :mappingStatus="mappingStatus"
        />

        <!-- 快捷筛选 -->
        <el-select
          v-if="fastFilter"
          v-model="fastSelVal"
          :placeholder="fastFilter.placeholder"
          style="width: 140px"
          clearable
          @change="applyFilter"
        >
          <el-option
            v-for="item in fastFilter.options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>

        <div class="filter-actions">
          <el-button
            type="primary"
            size="small"
            class="min-h-6 min-w-20"
            :icon="Search"
            @click="applyFilter"
          >
            {{ $t("buttons.pureSearch") }}
          </el-button>
          <el-button
            type="default"
            size="small"
            :icon="Refresh"
            class="min-h-6 min-w-20"
            @click="resetFilter"
          >
            {{ $t("buttons.pureReset") }}
          </el-button>
          <el-tooltip content="高级筛选" placement="top">
            <el-icon class="custom-tooltip" @click="showQualityFilter">
              <Tools />
            </el-icon>
          </el-tooltip>
          <ButtonGroup :buttons="topBtns" class="isFlex-default-none" />
        </div>
      </div>
    </div>
    <ButtonGroup :buttons="topBtns" class="isFlex" />
    <QualityInput
      ref="QualityFilterRef"
      :columns="columns"
      :mappingStatus="mappingStatus"
      @update:visible="handleQualityFilter"
    />
  </div>
</template>

<script setup>
import { nextTick, ref, onMounted, watch } from "vue";
import { Search, Tools, Refresh } from "@element-plus/icons-vue";
import QualityInput from "./qualityInput.vue";
import dayjs from "dayjs";
import filterInput from "./filterInput.vue";
import { debounce } from "lodash";
import { useI18n } from "vue-i18n";
import ButtonGroup from "@/components/ReTable/ButtonGroup.vue";
const { t } = useI18n(); // 解构出t方法

const props = defineProps({
  columns: {
    type: Array,
    required: true
  },
  topBtns: {
    type: Array,
    default: () => []
  },
  fastFilter: {
    type: Object,
    default: () => {}
  },
  /** 根据定义的枚举值进行字段搜索类型区分  * @type {Array} */
  mappingStatus: {
    type: Array,
    default: () => []
  },
  /** 查询数据，用于初始化筛选框  * @type {Object} */
  queryData: {
    type: Object,
    default: () => ({})
  }
});

// 用戶自定义筛选方案
const qualityFilters = ref([
  {
    id: 0,
    userId: "",
    schemeName: "",
    shareUserId: "",
    schemeContent: "",
    menuId: ""
  },
  {
    id: 0,
    userId: "",
    schemeName: "",
    shareUserId: "",
    schemeContent: "",
    menuId: ""
  }
]);

const emits = defineEmits(["update-filter", "reset-filter"]);

const QualityFilterRef = ref(null);

// 获取子组件的筛选数据
const filterInput1 = ref();
const filterInput2 = ref();
const selVal1 = ref("");
const selVal2 = ref("");

// 高级筛选
const advancedFilters = ref([]);

// 标记用户是否手动清空或重置了筛选框
const userManuallyReset = ref(false);

// 处理普通筛选
const processFilters = (selVal1, selVal2) => {
  const filters = [];
  const filtersPush = ({ selectedCondition1, selectedField1, inputValue1 }) => {
    if (selectedCondition1 && selectedField1 !== "" && inputValue1 !== "") {
      filters.push({
        disabled: true,
        name: selectedField1,
        condition: selectedCondition1,
        query: inputValue1,
        logic: "and"
      });
    }
  };
  filtersPush(selVal1);
  filtersPush(selVal2);
  return filters;
};

// 应用筛选过滤
const applyFilter = debounce(() => {
  nextTick(() => {
    try {
      selVal1.value = filterInput1.value.getChildrenSelVal();
      selVal2.value = filterInput2.value.getChildrenSelVal();

      // 检查是否有用户输入的筛选条件
      const hasUserInput =
        (selVal1.value &&
          selVal1.value.selectedField1 &&
          selVal1.value.inputValue1) ||
        (selVal2.value &&
          selVal2.value.selectedField1 &&
          selVal2.value.inputValue1);

      // 如果用户输入了筛选条件，设置标志
      if (hasUserInput) {
        userManuallyReset.value = true;
        console.log("用户手动输入了筛选条件，设置标志防止自动填充");
      }
    } catch (error) {
      console.error("Error while calling clearChildrenSelVal", error);
    }

    const filters = processFilters(selVal1.value, selVal2.value);

    if (fastSelVal.value) {
      filters.push({
        disabled: true,
        name: props.fastFilter.name,
        condition: "eq",
        query: fastSelVal.value,
        logic: "and"
      });
    }

    const combinedFilters = [...filters, ...advancedFilters.value];
    emits("update-filter", combinedFilters);
  });
}, 300);

// 重置过滤
const resetFilter = debounce(() => {
  nextTick(() => {
    // 设置标志，表示用户手动重置了筛选框
    userManuallyReset.value = true;
    console.log("用户手动重置了筛选框");

    filterInput1.value.clearChildrenSelVal(0);
    filterInput2.value.clearChildrenSelVal(1);
    advancedFilters.value = [];
    QualityFilterRef.value.clearAll();
    emits("reset-filter");
  });
}, 300);

// 快速筛选
const fastSelVal = ref("");

if (props.fastFilter?.default) {
  fastSelVal.value = props.fastFilter.default;
  applyFilter();
}

// 初始化筛选框，如果queryData中存在查询条件，则在第二个筛选框中默认填充
const initFilterFromQueryData = () => {
  console.log("初始化筛选框，queryData:", props.queryData);

  // 如果用户手动重置了筛选框，则不再自动填充
  if (userManuallyReset.value) {
    console.log("用户已手动重置筛选框，不再自动填充");
    return;
  }

  // 如果存在快捷筛选，则不对第二个筛选框进行自动填充
  if (props.fastFilter && Object.keys(props.fastFilter).length > 0) {
    console.log("存在快捷筛选，不对第二个筛选框进行自动填充");
    return;
  }

  if (
    props.queryData &&
    props.queryData.query &&
    props.queryData.query.length > 0
  ) {
    // 获取第一个查询条件
    const firstQuery = props.queryData.query[0];
    console.log("找到查询条件:", firstQuery);

    // 使用setTimeout确保组件完全挂载，并多次尝试
    const maxAttempts = 5;
    let attempts = 0;

    const trySetFilterValues = () => {
      // 再次检查用户是否手动重置了筛选框
      if (userManuallyReset.value) {
        console.log("用户已手动重置筛选框，停止尝试自动填充");
        return false;
      }

      // 再次检查是否存在快捷筛选
      if (props.fastFilter && Object.keys(props.fastFilter).length > 0) {
        console.log("存在快捷筛选，停止尝试自动填充第二个筛选框");
        return false;
      }

      attempts++;
      console.log(`尝试设置筛选框值，第${attempts}次尝试`);

      if (filterInput2.value && firstQuery) {
        console.log("设置第二个筛选框的值");
        // 设置第二个筛选框的值
        const field = firstQuery.name;
        const condition = firstQuery.condition;
        const value = firstQuery.query;

        console.log("字段:", field, "条件:", condition, "值:", value);

        // 查找对应的列
        const column = props.columns.find(col => col.prop === field);
        if (column) {
          console.log("找到对应列:", column);
          // 设置筛选框的值
          try {
            filterInput2.value.setFilterValues(field, condition, value);
            console.log("设置筛选框值成功");
            return true; // 成功设置
          } catch (error) {
            console.error("设置筛选框值失败:", error);
          }
        } else {
          console.warn("未找到对应列:", field);
        }
      } else {
        console.warn("filterInput2未初始化或没有查询条件");
      }

      // 如果还有尝试次数，继续尝试
      if (attempts < maxAttempts) {
        setTimeout(trySetFilterValues, 300);
      } else {
        console.error("设置筛选框值失败，已达到最大尝试次数");
      }

      return false;
    };

    // 开始尝试设置筛选框值
    setTimeout(trySetFilterValues, 300);
  } else {
    console.log("没有查询条件或queryData格式不正确");
  }
};

// 在组件挂载后初始化筛选框
onMounted(() => {
  console.log("组件已挂载，准备初始化筛选框");
  initFilterFromQueryData();
});

// 监听queryData变化，当queryData变化时重新初始化筛选框
watch(
  () => props.queryData,
  newVal => {
    console.log("queryData变化:", newVal);
    // 只有在用户未手动重置筛选框的情况下，才自动填充
    if (
      !userManuallyReset.value &&
      newVal &&
      newVal.query &&
      newVal.query.length > 0
    ) {
      initFilterFromQueryData();
    }
  },
  { deep: true, immediate: true }
);

// 显示高级筛选模态框
const showQualityFilter = () => {
  // 将默认筛选条件传递给高级筛选
  try {
    selVal1.value = filterInput1.value.getChildrenSelVal();
    selVal2.value = filterInput2.value.getChildrenSelVal();
  } catch (error) {
    console.error("Error while calling clearChildrenSelVal", error);
  }

  const filters = processFilters(selVal1.value, selVal2.value);

  QualityFilterRef.value.setDefaultFilters([...filters]);

  QualityFilterRef.value.showModal();
};

// 高级筛选传递
const handleQualityFilter = filter => {
  advancedFilters.value = filter;

  try {
    selVal1.value = filterInput1.value.getChildrenSelVal();
    selVal2.value = filterInput2.value.getChildrenSelVal();
  } catch (error) {
    console.error("Error while calling clearChildrenSelVal", error);
  }

  const filters = processFilters(selVal1.value, selVal2.value);
  // 高级筛选页执行查询
  emits("update-filter", [...filters, ...advancedFilters.value]);
};

// watch(
//   () => props.columns,
//   newColumns => {
//     if (newColumns.length > 0) {
//       selectedField1.value = newColumns[0].prop;
//       selectedField2.value = newColumns[1]?.prop || newColumns[0].prop;
//       updateFilteredConditions(selectedField1, filteredConditions1);
//       updateFilteredConditions(selectedField2, filteredConditions2);
//     }
//   },
//   { immediate: true }
// );
</script>

<style lang="scss" scoped>
.isFlex-default-none {
  display: none;
}

.isFlex {
  display: block;
}

@media (width <= 1600px) {
  .isFlex {
    display: none;
  }

  .isFlex-default-none {
    display: block;
  }
}

:deep(.el-button + .el-button) {
  margin-left: 0;
}

.custom-tooltip {
  cursor: pointer;
}

.flex {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.filter-component {
  display: flex;
  // flex-flow: column wrap;
  gap: 10px;
  justify-content: space-between;
  margin: 15px 0 2px;
}

.filter-set {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.filter-element {
  width: 200px;
}

.filter-element-type {
  width: 110px;
}

.filter-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center;
  justify-content: flex-end;
  min-width: 120px;
}

@media (width <= 450px) {
  .flex {
    flex-flow: nowrap;
  }

  .filter-set,
  .filter-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-element,
  .filter-element-type,
  .filter-actions {
    width: 100% !important;
  }
}
</style>
